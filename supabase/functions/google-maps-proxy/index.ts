import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

// CORS headers
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
  "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
};

// Types for Google Maps API
interface PlacesAutocompleteRequest {
  input: string;
  types?: string[];
  componentRestrictions?: { country: string };
  bounds?: {
    northeast: { lat: number; lng: number };
    southwest: { lat: number; lng: number };
  };
}

interface PlaceDetailsRequest {
  place_id: string;
  fields?: string[];
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Verify authentication
    const authHeader = req.headers.get('Authorization');
    console.log('Auth header present:', !!authHeader);

    if (!authHeader) {
      console.error('Missing authorization header');
      return new Response(
        JSON.stringify({ error: 'Missing authorization header' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Initialize Supabase client with service role for JWT verification
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Extract JWT token and verify it using service role
    const token = authHeader.replace('Bearer ', '');
    console.log('Token length:', token.length);
    console.log('Token starts with:', token.substring(0, 20) + '...');

    let user;
    try {
      // Use service role client to verify the JWT token
      const { data, error } = await supabaseClient.auth.getUser(token);
      if (error) {
        console.error('Auth error:', error.message, 'Code:', error.status);

        // If JWT is invalid, try alternative verification
        if (error.message.includes('missing sub claim') || error.message.includes('bad_jwt')) {
          console.log('JWT verification failed, trying alternative approach...');

          // Try to verify using the anon key client with the token in headers
          const anonClient = createClient(
            Deno.env.get('SUPABASE_URL') ?? '',
            Deno.env.get('SUPABASE_ANON_KEY') ?? '',
            {
              global: {
                headers: { Authorization: authHeader },
              },
            }
          );

          const { data: altData, error: altError } = await anonClient.auth.getUser();
          if (altError || !altData.user) {
            throw new Error(`Authentication failed: ${error.message}`);
          }
          user = altData.user;
          console.log('Alternative auth successful for user:', user.id);
        } else {
          throw error;
        }
      } else {
        user = data.user;
        console.log('Direct JWT verification successful for user:', user?.id);
      }
    } catch (authError) {
      console.error('All authentication methods failed:', authError);
      return new Response(
        JSON.stringify({
          error: 'Authentication failed',
          details: authError.message,
          hint: 'Please refresh your session and try again'
        }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    if (!user) {
      console.error('No user found after authentication');
      return new Response(
        JSON.stringify({ error: 'No user found' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    console.log('User authenticated successfully:', user.id);

    // Get Google Maps API key from environment
    const googleMapsApiKey = Deno.env.get('GOOGLE_MAPS_API_KEY');
    if (!googleMapsApiKey) {
      console.error('Google Maps API key not configured');
      return new Response(
        JSON.stringify({ error: 'Google Maps service not configured' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    const { action, ...requestData } = await req.json();

    // Handle different Google Maps API actions
    switch (action) {
      case 'autocomplete':
        return await handleAutocomplete(requestData as PlacesAutocompleteRequest, googleMapsApiKey, user.id, supabaseClient);
      
      case 'place_details':
        return await handlePlaceDetails(requestData as PlaceDetailsRequest, googleMapsApiKey, user.id, supabaseClient);
      
      default:
        return new Response(
          JSON.stringify({ error: 'Invalid action specified' }),
          { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
    }

  } catch (error) {
    console.error('Google Maps proxy error:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
});

// Handle Places Autocomplete requests
async function handleAutocomplete(
  request: PlacesAutocompleteRequest, 
  apiKey: string, 
  userId: string, 
  supabaseClient: any
) {
  try {
    // Build Google Maps Autocomplete API URL
    const params = new URLSearchParams({
      input: request.input,
      key: apiKey,
      types: request.types?.join('|') || 'geocode',
      components: request.componentRestrictions?.country ? `country:${request.componentRestrictions.country}` : 'country:IN',
    });

    // Add bounds if provided (for Delhi NCR restriction)
    if (request.bounds) {
      const { northeast, southwest } = request.bounds;
      params.append('bounds', `${southwest.lat},${southwest.lng}|${northeast.lat},${northeast.lng}`);
      params.append('strictbounds', 'false'); // Allow some flexibility
    }

    const url = `https://maps.googleapis.com/maps/api/place/autocomplete/json?${params.toString()}`;

    // Make request to Google Maps API
    const response = await fetch(url);
    const data = await response.json();

    // Log API usage for monitoring
    await supabaseClient.from('security_logs').insert({
      event_type: 'GOOGLE_MAPS_AUTOCOMPLETE_REQUEST',
      severity: 'LOW',
      details: {
        user_id: userId,
        query_length: request.input.length,
        results_count: data.predictions?.length || 0,
        timestamp: new Date().toISOString()
      },
      user_id: userId
    });

    return new Response(
      JSON.stringify(data),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error('Autocomplete request failed:', error);
    return new Response(
      JSON.stringify({ error: 'Autocomplete request failed' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
}

// Handle Place Details requests
async function handlePlaceDetails(
  request: PlaceDetailsRequest, 
  apiKey: string, 
  userId: string, 
  supabaseClient: any
) {
  try {
    // Build Google Maps Place Details API URL
    const params = new URLSearchParams({
      place_id: request.place_id,
      key: apiKey,
      fields: request.fields?.join(',') || 'formatted_address,geometry,name,place_id,types',
    });

    const url = `https://maps.googleapis.com/maps/api/place/details/json?${params.toString()}`;

    // Make request to Google Maps API
    const response = await fetch(url);
    const data = await response.json();

    // Log API usage for monitoring
    await supabaseClient.from('security_logs').insert({
      event_type: 'GOOGLE_MAPS_PLACE_DETAILS_REQUEST',
      severity: 'LOW',
      details: {
        user_id: userId,
        place_id: request.place_id,
        timestamp: new Date().toISOString()
      },
      user_id: userId
    });

    return new Response(
      JSON.stringify(data),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );

  } catch (error) {
    console.error('Place details request failed:', error);
    return new Response(
      JSON.stringify({ error: 'Place details request failed' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
}
