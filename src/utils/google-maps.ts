import { Address } from '@/types/location';
import {
  GoogleMapsConfig,
  PlacesPrediction,
  PlaceDetailsResult,
  AutocompleteRequest,
  PlaceDetailsRequest,
  DELHI_NCR_BOUNDS,
  GoogleMapsStatus,
  AddressComponentTypes
} from '@/types/google-maps';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client for secure API calls
const supabase = createClient(
  import.meta.env.VITE_SUPABASE_URL,
  import.meta.env.VITE_SUPABASE_ANON_KEY
);

class GoogleMapsService {
  private searchCache = new Map<string, Address[]>();
  private cacheExpiry = 5 * 60 * 1000; // 5 minutes

  // 🔒 SECURITY: Removed direct API key usage
  // All Google Maps requests now go through secure Edge Function proxy

  // 🔒 SECURITY: Removed Google Maps API loading
  // All requests now go through secure Supabase Edge Function proxy
  async loadGoogleMapsAPI(): Promise<boolean> {
    // Always return true since we're using Edge Function proxy
    // No need to load Google Maps JavaScript API directly
    return true;
  }

      // Set up callback
      return new Promise((resolve, reject) => {
        window.initGoogleMaps = () => {
          try {
            this.initializeServices();
            this.isLoaded = true;
            this.isLoading = false;
            resolve(true);
          } catch (error) {
            console.error('Failed to initialize Google Maps services:', error);
            this.isLoading = false;
            reject(false);
          }
        };

        script.onerror = () => {
          console.error('Failed to load Google Maps API');
          this.isLoading = false;
          reject(false);
        };

        document.head.appendChild(script);

        // Timeout after 10 seconds
        setTimeout(() => {
          if (!this.isLoaded) {
            console.error('Google Maps API loading timeout');
            this.isLoading = false;
            reject(false);
          }
        }, 10000);
      });
    } catch (error) {
      console.error('Error loading Google Maps API:', error);
      this.isLoading = false;
      return false;
    }
  }

  // Initialize Google Maps services
  private initializeServices(): void {
    if (!window.google?.maps?.places) {
      throw new Error('Google Maps Places API not available');
    }

    this.autocompleteService = new window.google.maps.places.AutocompleteService();
    
    // Create a dummy div for PlacesService (required by API)
    const dummyDiv = document.createElement('div');
    this.placesService = new window.google.maps.places.PlacesService(dummyDiv);
  }

  // 🔒 SECURE: Search for address suggestions using Edge Function proxy
  async searchAddresses(query: string): Promise<Address[]> {
    if (query.length < 2) return [];

    // Check cache first
    const cacheKey = query.toLowerCase();
    const cached = this.searchCache.get(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      // Use secure Edge Function proxy instead of direct API call
      const { data, error } = await supabase.functions.invoke('google-maps-proxy', {
        body: {
          action: 'autocomplete',
          input: query,
          types: ['geocode'],
          componentRestrictions: { country: 'IN' },
          bounds: DELHI_NCR_BOUNDS
        }
      });

      if (error) {
        console.warn('Google Maps proxy failed:', error);
        return [];
      }

      if (!data.predictions || data.status !== 'OK') {
        console.warn('Google Places Autocomplete failed:', data.status);
        return [];
      }

      // Filter predictions for Delhi NCR area
      const delhiPredictions = data.predictions.filter((prediction: any) =>
        this.isDelhiNCRLocation(prediction.description)
      );

      // Convert predictions to Address objects
      const addresses = await Promise.all(
        delhiPredictions.slice(0, 5).map((prediction: any) =>
          this.convertPredictionToAddress(prediction)
        )
      );

      // Filter out null results
      const validAddresses = addresses.filter(addr => addr !== null) as Address[];

      // Cache results
      this.searchCache.set(cacheKey, validAddresses);
      setTimeout(() => this.searchCache.delete(cacheKey), this.cacheExpiry);

      return validAddresses;

    } catch (error) {
      console.error('Secure Google Maps search failed:', error);
      return [];
    }
  }

  // Check if location is in Delhi NCR area
  private isDelhiNCRLocation(description: string): boolean {
    const lowerDesc = description.toLowerCase();
    return (
      lowerDesc.includes('delhi') ||
      lowerDesc.includes('new delhi') ||
      lowerDesc.includes('gurgaon') ||
      lowerDesc.includes('gurugram') ||
      lowerDesc.includes('noida') ||
      lowerDesc.includes('faridabad') ||
      lowerDesc.includes('ghaziabad') ||
      lowerDesc.includes('ncr')
    );
  }

  // 🔒 SECURE: Convert Google Places prediction to Address interface using Edge Function
  private async convertPredictionToAddress(prediction: any): Promise<Address | null> {
    try {
      // Use secure Edge Function proxy for place details
      const { data, error } = await supabase.functions.invoke('google-maps-proxy', {
        body: {
          action: 'place_details',
          place_id: prediction.place_id,
          fields: ['address_components', 'formatted_address', 'geometry', 'name']
        }
      });

      if (error || !data.result || data.status !== 'OK') {
        console.warn('Place details request failed:', data?.status || error);
        // Fallback to basic parsing from prediction
        return this.parseBasicAddress(prediction);
      }

      return this.parseDetailedAddress(data.result);

    } catch (error) {
      console.error('Secure place details request failed:', error);
      // Fallback to basic parsing from prediction
      return this.parseBasicAddress(prediction);
    }
  }

  // Parse address from place details with coordinates (API response format)
  private parseDetailedAddress(placeDetails: any): Address {
    const components = placeDetails.address_components;
    const address: Address = {
      display_name: placeDetails.formatted_address
    };

    // Extract coordinates from geometry (API response format)
    if (placeDetails.geometry?.location) {
      (address as any).coordinates = {
        latitude: placeDetails.geometry.location.lat,
        longitude: placeDetails.geometry.location.lng
      };
    }

    components.forEach(component => {
      const types = component.types;

      if (types.includes(AddressComponentTypes.ROUTE)) {
        address.street = component.long_name;
      } else if (types.includes(AddressComponentTypes.SUBLOCALITY_LEVEL_1)) {
        address.area = component.long_name;
      } else if (types.includes(AddressComponentTypes.LOCALITY)) {
        address.city = component.long_name;
      } else if (types.includes(AddressComponentTypes.ADMINISTRATIVE_AREA_LEVEL_1)) {
        address.state = component.long_name;
      } else if (types.includes(AddressComponentTypes.COUNTRY)) {
        address.country = component.long_name;
      } else if (types.includes(AddressComponentTypes.POSTAL_CODE)) {
        address.postal_code = component.long_name;
      }
    });

    // Ensure we have city/area information
    if (!address.city && !address.area) {
      address.area = placeDetails.name;
    }

    return address;
  }

  // Fallback parsing from prediction only (API response format)
  private parseBasicAddress(prediction: any): Address {
    const terms = prediction.terms;
    const address: Address = {
      display_name: prediction.description
    };

    if (terms.length > 0) {
      address.area = terms[0].value;
    }
    if (terms.length > 1) {
      address.city = terms[1].value;
    }
    if (terms.length > 2) {
      address.state = terms[2].value;
    }

    return address;
  }

  // Clear cache
  clearCache(): void {
    this.searchCache.clear();
  }

  // Check if API is loaded
  isAPILoaded(): boolean {
    return this.isLoaded;
  }
}

// Export singleton instance
export const googleMapsService = new GoogleMapsService();
